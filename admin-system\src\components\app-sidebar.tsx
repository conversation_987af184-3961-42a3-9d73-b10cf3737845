"use client"

import * as React from "react"
import {
  BarChart3,
  Users,
  Settings,
  Shield,
  Activity,
  FileText,
  Database,
  Bell,
  HelpCircle,
  LogOut,
  Home,
  UserPlus,
  UserCheck,
  UserX,
  BarChart,
  TrendingUp,
  Calendar,
  Mail,
  MessageSquare,
  ChevronDown,
  ChevronRight,
} from "lucide-react"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import { useRouter, usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

// 菜单数据
const data = {
  navMain: [
    {
      title: "仪表板",
      url: "/dashboard",
      icon: Home,
    },
    {
      title: "用户管理",
      url: "/users",
      icon: Users,
    },
    {
      title: "数据分析",
      url: "/analytics",
      icon: BarChart3,
    },
    {
      title: "系统设置",
      url: "/settings",
      icon: Settings,
    },
  ],
  navSecondary: [
    {
      title: "帮助中心",
      url: "/help",
      icon: HelpCircle,
    },
  ],
}

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

export function AppSidebar({ isCollapsed, onToggle }: SidebarProps) {
  const { user, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const handleLogout = async () => {
    await logout()
    router.push("/login")
  }

  const isActive = (url: string) => pathname === url

  return (
    <div className={cn(
      "flex h-full flex-col border-r bg-background transition-all duration-300",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="flex h-16 items-center border-b px-4">
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <BarChart3 className="h-4 w-4" />
          </div>
          {!isCollapsed && (
            <div className="flex flex-col">
              <span className="text-sm font-semibold">管理系统</span>
              <span className="text-xs text-muted-foreground">Admin Dashboard</span>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-auto py-4">
        <div className="space-y-2 px-3">
          {!isCollapsed && (
            <div className="px-3 py-2">
              <h2 className="mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                主要功能
              </h2>
            </div>
          )}

          {data.navMain.map((item) => {
            const isExpanded = expandedItems.includes(item.title)
            const hasSubItems = item.items && item.items.length > 0
            const itemIsActive = isParentActive(item)

            return (
              <div key={item.title}>
                <Button
                  variant={itemIsActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start h-10",
                    isCollapsed ? "px-2" : "px-3",
                    itemIsActive && "bg-secondary"
                  )}
                  onClick={() => {
                    if (hasSubItems && !isCollapsed) {
                      toggleExpanded(item.title)
                    } else {
                      router.push(item.url)
                    }
                  }}
                >
                  <item.icon className="h-4 w-4 shrink-0" />
                  {!isCollapsed && (
                    <>
                      <span className="ml-2 flex-1 text-left">{item.title}</span>
                      {hasSubItems && (
                        <ChevronDown className={cn(
                          "h-4 w-4 transition-transform",
                          isExpanded && "rotate-180"
                        )} />
                      )}
                    </>
                  )}
                </Button>

                {/* Sub items */}
                {hasSubItems && !isCollapsed && isExpanded && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.items.map((subItem) => (
                      <Button
                        key={subItem.title}
                        variant={isActive(subItem.url) ? "secondary" : "ghost"}
                        size="sm"
                        className="w-full justify-start h-8"
                        onClick={() => router.push(subItem.url)}
                      >
                        <subItem.icon className="h-3 w-3 shrink-0" />
                        <span className="ml-2">{subItem.title}</span>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Secondary Navigation */}
        <div className="mt-8 space-y-2 px-3">
          {data.navSecondary.map((item) => (
            <Button
              key={item.title}
              variant="ghost"
              className={cn(
                "w-full justify-start h-10",
                isCollapsed ? "px-2" : "px-3"
              )}
              onClick={() => router.push(item.url)}
            >
              <item.icon className="h-4 w-4 shrink-0" />
              {!isCollapsed && <span className="ml-2">{item.title}</span>}
            </Button>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="border-t p-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start h-12",
                isCollapsed ? "px-2" : "px-3"
              )}
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar_url} alt={user?.username} />
                <AvatarFallback>
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              {!isCollapsed && (
                <div className="ml-2 flex flex-1 flex-col items-start">
                  <span className="text-sm font-medium">{user?.full_name || user?.username}</span>
                  <span className="text-xs text-muted-foreground">{user?.email}</span>
                </div>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium">{user?.full_name || user?.username}</p>
                <p className="text-xs text-muted-foreground">{user?.email}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push('/profile')}>
              <Settings className="mr-2 h-4 w-4" />
              个人设置
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push('/help')}>
              <HelpCircle className="mr-2 h-4 w-4" />
              帮助中心
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              退出登录
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
