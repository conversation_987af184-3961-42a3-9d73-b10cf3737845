"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/layout",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 菜单数据\nconst data = {\n    navMain: [\n        {\n            title: \"仪表板\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            title: \"用户管理\",\n            url: \"/users\",\n            icon: _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            title: \"数据分析\",\n            url: \"/analytics\",\n            icon: _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            title: \"系统设置\",\n            url: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ],\n    navSecondary: [\n        {\n            title: \"帮助中心\",\n            url: \"/help\",\n            icon: _barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { isCollapsed, onToggle } = param;\n    var _user_username;\n    _s();\n    const { user, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleLogout = async ()=>{\n        await logout();\n        router.push(\"/login\");\n    };\n    const isActive = (url)=>pathname === url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"flex h-full flex-col border-r bg-background transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center border-b px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"管理系统\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Admin Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 px-3\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wide\",\n                                    children: \"主要功能\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            data.navMain.map((item)=>{\n                                const isExpanded = expandedItems.includes(item.title);\n                                const hasSubItems = item.items && item.items.length > 0;\n                                const itemIsActive = isParentActive(item);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: itemIsActive ? \"secondary\" : \"ghost\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-10\", isCollapsed ? \"px-2\" : \"px-3\", itemIsActive && \"bg-secondary\"),\n                                            onClick: ()=>{\n                                                if (hasSubItems && !isCollapsed) {\n                                                    toggleExpanded(item.title);\n                                                } else {\n                                                    router.push(item.url);\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"h-4 w-4 shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 flex-1 text-left\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasSubItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"h-4 w-4 transition-transform\", isExpanded && \"rotate-180\")\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        hasSubItems && !isCollapsed && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 mt-1 space-y-1\",\n                                            children: item.items.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: isActive(subItem.url) ? \"secondary\" : \"ghost\",\n                                                    size: \"sm\",\n                                                    className: \"w-full justify-start h-8\",\n                                                    onClick: ()=>router.push(subItem.url),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(subItem.icon, {\n                                                            className: \"h-3 w-3 shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2\",\n                                                            children: subItem.title\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, subItem.title, true, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.title, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 space-y-2 px-3\",\n                        children: data.navSecondary.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-10\", isCollapsed ? \"px-2\" : \"px-3\"),\n                                onClick: ()=>router.push(item.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"h-4 w-4 shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, item.title, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-12\", isCollapsed ? \"px-2\" : \"px-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                src: user === null || user === void 0 ? void 0 : user.avatar_url,\n                                                alt: user === null || user === void 0 ? void 0 : user.username\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                children: (user === null || user === void 0 ? void 0 : (_user_username = user.username) === null || _user_username === void 0 ? void 0 : _user_username.charAt(0).toUpperCase()) || 'U'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2 flex flex-1 flex-col items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: user === null || user === void 0 ? void 0 : user.email\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                            align: \"end\",\n                            className: \"w-56\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: user === null || user === void 0 ? void 0 : user.email\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: ()=>router.push('/profile'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"个人设置\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: ()=>router.push('/help'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"帮助中心\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"退出登录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"TVCmUMOhI9c7KC/d7gLgXSNlw8w=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});