import App from './App'
import api from './utils/api.js'
import API from './api/index.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 全局注册API工具
Vue.prototype.$api = api
Vue.prototype.$API = API

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 全局注册API工具
  app.config.globalProperties.$api = api
  app.config.globalProperties.$API = API

  return {
    app
  }
}
// #endif