/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRTpcXDdtb3V0aE1pc3Npb25cXHVuaUZpZ21hXFxhZG1pbi1zeXN0ZW1cXG5vZGVfbW9kdWxlc1xcLnBucG1cXG5leHRAMTUuNC40X3JlYWN0LWRvbUAxOS4xLjBfcmVhY3RAMTkuMS4wX19yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C7mouthMission%5C%5CuniFigma%5C%5Cadmin-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C7mouthMission%5C%5CuniFigma%5C%5Cadmin-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Qzdtb3V0aE1pc3Npb24lNUMlNUN1bmlGaWdtYSU1QyU1Q2FkbWluLXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQWtHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFw3bW91dGhNaXNzaW9uXFxcXHVuaUZpZ21hXFxcXGFkbWluLXN5c3RlbVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C7mouthMission%5C%5CuniFigma%5C%5Cadmin-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*********************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjQuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx5UUFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFw3bW91dGhNaXNzaW9uXFx1bmlGaWdtYVxcYWRtaW4tc3lzdGVtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxuZXh0QDE1LjQuNF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const { isAuthenticated, loading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!loading) {\n                if (isAuthenticated) {\n                    router.push('/dashboard');\n                } else {\n                    router.push('/login');\n                }\n            }\n        }\n    }[\"Home.useEffect\"], [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    return null;\n}\n_s(Home, \"hfJApfKdkCGjZCs1NVF94JuC2xc=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFa0M7QUFDVTtBQUNNO0FBRW5DLFNBQVNHOztJQUN0QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsT0FBTyxFQUFFLEdBQUdILCtEQUFPQTtJQUM1QyxNQUFNSSxTQUFTTCwwREFBU0E7SUFFeEJELGdEQUFTQTswQkFBQztZQUNSLElBQUksQ0FBQ0ssU0FBUztnQkFDWixJQUFJRCxpQkFBaUI7b0JBQ25CRSxPQUFPQyxJQUFJLENBQUM7Z0JBQ2QsT0FBTztvQkFDTEQsT0FBT0MsSUFBSSxDQUFDO2dCQUNkO1lBQ0Y7UUFDRjt5QkFBRztRQUFDSDtRQUFpQkM7UUFBU0M7S0FBTztJQUVyQyxJQUFJRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNHO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzs7Ozs7Ozs7OztJQUdyQjtJQUVBLE9BQU87QUFDVDtHQXZCd0JOOztRQUNlRCwyREFBT0E7UUFDN0JELHNEQUFTQTs7O0tBRkZFIiwic291cmNlcyI6WyJFOlxcN21vdXRoTWlzc2lvblxcdW5pRmlnbWFcXGFkbWluLXN5c3RlbVxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9hdXRoLWNvbnRleHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgbG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbG9hZGluZykge1xuICAgICAgaWYgKGlzQXV0aGVudGljYXRlZCkge1xuICAgICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgbG9hZGluZywgcm91dGVyXSk7XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1zY3JlZW5cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMzIgdy0zMiBib3JkZXItYi0yIGJvcmRlci1ncmF5LTkwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiBudWxsO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJIb21lIiwiaXNBdXRoZW50aWNhdGVkIiwibG9hZGluZyIsInJvdXRlciIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withRole: () => (/* binding */ withRole)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services */ \"(app-pages-browser)/./src/services/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,withRole auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Create context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Auth provider component\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check for existing session on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const storedToken = localStorage.getItem('auth_token');\n            const storedUser = localStorage.getItem('auth_user');\n            if (storedToken && storedUser) {\n                try {\n                    setToken(storedToken);\n                    setUser(JSON.parse(storedUser));\n                } catch (error) {\n                    console.error('Error parsing stored user data:', error);\n                    localStorage.removeItem('auth_token');\n                    localStorage.removeItem('auth_user');\n                }\n            }\n            setLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Login function\n    const login = async (emailOrUsername, password)=>{\n        try {\n            const response = await _services__WEBPACK_IMPORTED_MODULE_2__.AuthService.login({\n                emailOrUsername,\n                password\n            });\n            if (response.success && response.data) {\n                setUser(response.data.user);\n                setToken(response.data.token);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || 'Login failed'\n                };\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                error: error.message || 'Network error. Please try again.'\n            };\n        }\n    };\n    // Register function\n    const register = async (userData)=>{\n        try {\n            const response = await _services__WEBPACK_IMPORTED_MODULE_2__.AuthService.register(userData);\n            if (response.success && response.data) {\n                setUser(response.data.user);\n                setToken(response.data.token);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || 'Registration failed'\n                };\n            }\n        } catch (error) {\n            console.error('Registration error:', error);\n            return {\n                success: false,\n                error: error.message || 'Network error. Please try again.'\n            };\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            await _services__WEBPACK_IMPORTED_MODULE_2__.AuthService.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            setUser(null);\n            setToken(null);\n        }\n    };\n    const value = {\n        user,\n        token,\n        login,\n        register,\n        logout,\n        loading,\n        isAuthenticated: !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\n// Hook to use auth context\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// HOC for protected routes\nfunction withAuth(Component) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Please log in to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n            lineNumber: 154,\n            columnNumber: 12\n        }, this);\n    }, \"F3aPsg481KjBH7Z7iYl6LJifZz0=\", false, function() {\n        return [\n            useAuth\n        ];\n    });\n}\n// HOC for role-based access\nfunction withRole(Component, allowedRoles) {\n    var _s = $RefreshSig$();\n    return _s(function RoleProtectedComponent(props) {\n        _s();\n        const { user, isAuthenticated, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated || !user) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Please log in to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this);\n        }\n        if (!allowedRoles.includes(user.role)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Insufficient Permissions\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"You don't have permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\contexts\\\\auth-context.tsx\",\n            lineNumber: 196,\n            columnNumber: 12\n        }, this);\n    }, \"PyZyUXrt2JnESGKghmff/gJjrqw=\", false, function() {\n        return [\n            useAuth\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   ApiClientError: () => (/* binding */ ApiClientError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Admin System API 客户端\n * 提供类型安全的API调用接口\n */ // API 配置\nconst API_CONFIG = {\n    baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',\n    timeout: 10000,\n    retryCount: 3,\n    retryDelay: 1000\n};\n// 错误类型\nclass ApiClientError extends Error {\n    constructor(statusCode, message, code, data){\n        super(message), this.statusCode = statusCode, this.code = code, this.data = data;\n        this.name = 'ApiClientError';\n    }\n}\n// Token 管理\nclass TokenManager {\n    static getToken() {\n        if (false) {}\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (false) {}\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static clearToken() {\n        if (false) {}\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.USER_KEY);\n    }\n    static getUser() {\n        if (false) {}\n        const userStr = localStorage.getItem(this.USER_KEY);\n        return userStr ? JSON.parse(userStr) : null;\n    }\n    static setUser(user) {\n        if (false) {}\n        localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n}\nTokenManager.TOKEN_KEY = 'auth_token';\nTokenManager.USER_KEY = 'auth_user';\n// 请求拦截器\nfunction requestInterceptor(url, config) {\n    const token = TokenManager.getToken();\n    const headers = {\n        'Content-Type': 'application/json',\n        ...config.headers\n    };\n    if (token) {\n        headers.Authorization = \"Bearer \".concat(token);\n    }\n    return {\n        ...config,\n        headers\n    };\n}\n// 响应拦截器\nasync function responseInterceptor(response) {\n    const contentType = response.headers.get('content-type');\n    let data;\n    if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n    } else {\n        data = await response.text();\n    }\n    if (!response.ok) {\n        // HTTP 错误\n        if (response.status === 401) {\n            // 未授权，清除token\n            TokenManager.clearToken();\n            // 如果在浏览器环境且不在登录页，跳转到登录页\n            if ( true && !window.location.pathname.includes('/login')) {\n                window.location.href = '/login';\n            }\n        }\n        throw new ApiClientError(response.status, (data === null || data === void 0 ? void 0 : data.error) || (data === null || data === void 0 ? void 0 : data.message) || \"HTTP \".concat(response.status), data === null || data === void 0 ? void 0 : data.code, data);\n    }\n    // 业务逻辑错误检查\n    if (data && typeof data === 'object' && data.success === false) {\n        throw new ApiClientError(response.status, data.error || data.message || '请求失败', data.code, data);\n    }\n    return data;\n}\n// 重试机制\nasync function withRetry(requestFn) {\n    let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : API_CONFIG.retryCount;\n    let lastError;\n    for(let i = 0; i <= retryCount; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // 如果是客户端错误（4xx）或最后一次重试，直接抛出错误\n            if (error instanceof ApiClientError && error.statusCode < 500) {\n                throw error;\n            }\n            if (i === retryCount) {\n                throw error;\n            }\n            // 等待后重试\n            await new Promise((resolve)=>setTimeout(resolve, API_CONFIG.retryDelay * (i + 1)));\n        }\n    }\n    throw lastError;\n}\n// 核心请求函数\nasync function request(endpoint) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_CONFIG.baseURL).concat(endpoint);\n    const interceptedConfig = requestInterceptor(url, config);\n    const requestFn = async ()=>{\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), config.timeout || API_CONFIG.timeout);\n        try {\n            const response = await fetch(url, {\n                method: config.method || 'GET',\n                headers: interceptedConfig.headers,\n                body: config.body ? JSON.stringify(config.body) : undefined,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return await responseInterceptor(response);\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof DOMException && error.name === 'AbortError') {\n                throw new ApiClientError(408, '请求超时', 'TIMEOUT');\n            }\n            throw error;\n        }\n    };\n    return withRetry(requestFn, config.retryCount);\n}\n// API 客户端类\nclass ApiClient {\n    // GET 请求\n    static async get(endpoint, params, config) {\n        let url = endpoint;\n        if (params) {\n            const searchParams = new URLSearchParams();\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    searchParams.append(key, String(value));\n                }\n            });\n            const queryString = searchParams.toString();\n            if (queryString) {\n                url += \"?\".concat(queryString);\n            }\n        }\n        return request(url, {\n            ...config,\n            method: 'GET'\n        });\n    }\n    // POST 请求\n    static async post(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'POST',\n            body: data\n        });\n    }\n    // PUT 请求\n    static async put(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'PUT',\n            body: data\n        });\n    }\n    // DELETE 请求\n    static async delete(endpoint, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'DELETE'\n        });\n    }\n    // PATCH 请求\n    static async patch(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'PATCH',\n            body: data\n        });\n    }\n    // 配置方法\n    static setBaseURL(url) {\n        API_CONFIG.baseURL = url;\n    }\n    static setTimeout(timeout) {\n        API_CONFIG.timeout = timeout;\n    }\n    static setRetryConfig(count, delay) {\n        API_CONFIG.retryCount = count;\n        API_CONFIG.retryDelay = delay;\n    }\n}\n// Token 管理方法\nApiClient.getToken = TokenManager.getToken;\nApiClient.setToken = TokenManager.setToken;\nApiClient.clearToken = TokenManager.clearToken;\nApiClient.getUser = TokenManager.getUser;\nApiClient.setUser = TokenManager.setUser;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ApiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/auth.service.ts":
/*!**************************************!*\
  !*** ./src/services/auth.service.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/**\n * 认证服务\n * 处理用户认证相关的API调用\n */ \n// 认证服务类\nclass AuthService {\n    /**\n   * 用户登录\n   */ static async login(data) {\n        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/login', data);\n        // 登录成功后保存用户信息和token\n        if (response.success && response.data) {\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setToken(response.data.token);\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setUser(response.data.user);\n        }\n        return response;\n    }\n    /**\n   * 用户注册\n   */ static async register(data) {\n        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/register', data);\n        // 注册成功后保存用户信息和token\n        if (response.success && response.data) {\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setToken(response.data.token);\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setUser(response.data.user);\n        }\n        return response;\n    }\n    /**\n   * 用户登出\n   */ static async logout() {\n        try {\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/logout');\n            return response;\n        } finally{\n            // 无论请求是否成功，都清除本地存储\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearToken();\n        }\n    }\n    /**\n   * 刷新token\n   */ static async refreshToken() {\n        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/refresh');\n        if (response.success && response.data) {\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setToken(response.data.token);\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setUser(response.data.user);\n        }\n        return response;\n    }\n    /**\n   * 获取当前用户信息\n   */ static async getCurrentUser() {\n        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/auth/me');\n        if (response.success && response.data) {\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setUser(response.data);\n        }\n        return response;\n    }\n    /**\n   * 更新用户资料\n   */ static async updateProfile(data) {\n        const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put('/auth/profile', data);\n        if (response.success && response.data) {\n            _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setUser(response.data);\n        }\n        return response;\n    }\n    /**\n   * 修改密码\n   */ static async changePassword(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put('/auth/change-password', data);\n    }\n    /**\n   * 忘记密码\n   */ static async forgotPassword(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/forgot-password', data);\n    }\n    /**\n   * 重置密码\n   */ static async resetPassword(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/auth/reset-password', data);\n    }\n    /**\n   * 验证token是否有效\n   */ static async validateToken() {\n        try {\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/auth/validate');\n            return response.success;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * 获取用户权限\n   */ static async getUserPermissions() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/auth/permissions');\n    }\n    /**\n   * 检查用户是否有特定权限\n   */ static async hasPermission(permission) {\n        try {\n            var _response_data;\n            const response = await this.getUserPermissions();\n            return response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.includes(permission)) || false;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * 获取用户角色\n   */ static async getUserRoles() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/auth/roles');\n    }\n    /**\n   * 检查用户是否有特定角色\n   */ static async hasRole(role) {\n        try {\n            var _response_data;\n            const response = await this.getUserRoles();\n            return response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.includes(role)) || false;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * 获取本地存储的用户信息\n   */ static getLocalUser() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getUser();\n    }\n    /**\n   * 获取本地存储的token\n   */ static getLocalToken() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getToken();\n    }\n    /**\n   * 检查用户是否已登录\n   */ static isAuthenticated() {\n        const token = this.getLocalToken();\n        const user = this.getLocalUser();\n        return !!(token && user);\n    }\n    /**\n   * 清除认证信息\n   */ static clearAuth() {\n        _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearToken();\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9hdXRoLnNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7OztDQUdDLEdBRXdDO0FBaUR6QyxRQUFRO0FBQ0QsTUFBTUM7SUFDWDs7R0FFQyxHQUNELGFBQWFDLE1BQU1DLElBQWtCLEVBQXNDO1FBQ3pFLE1BQU1DLFdBQVcsTUFBTUosdURBQVNBLENBQUNLLElBQUksQ0FBZSxlQUFlRjtRQUVuRSxvQkFBb0I7UUFDcEIsSUFBSUMsU0FBU0UsT0FBTyxJQUFJRixTQUFTRCxJQUFJLEVBQUU7WUFDckNILHVEQUFTQSxDQUFDTyxRQUFRLENBQUNILFNBQVNELElBQUksQ0FBQ0ssS0FBSztZQUN0Q1IsdURBQVNBLENBQUNTLE9BQU8sQ0FBQ0wsU0FBU0QsSUFBSSxDQUFDTyxJQUFJO1FBQ3RDO1FBRUEsT0FBT047SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYU8sU0FBU1IsSUFBcUIsRUFBc0M7UUFDL0UsTUFBTUMsV0FBVyxNQUFNSix1REFBU0EsQ0FBQ0ssSUFBSSxDQUFlLGtCQUFrQkY7UUFFdEUsb0JBQW9CO1FBQ3BCLElBQUlDLFNBQVNFLE9BQU8sSUFBSUYsU0FBU0QsSUFBSSxFQUFFO1lBQ3JDSCx1REFBU0EsQ0FBQ08sUUFBUSxDQUFDSCxTQUFTRCxJQUFJLENBQUNLLEtBQUs7WUFDdENSLHVEQUFTQSxDQUFDUyxPQUFPLENBQUNMLFNBQVNELElBQUksQ0FBQ08sSUFBSTtRQUN0QztRQUVBLE9BQU9OO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGFBQWFRLFNBQXFDO1FBQ2hELElBQUk7WUFDRixNQUFNUixXQUFXLE1BQU1KLHVEQUFTQSxDQUFDSyxJQUFJLENBQU87WUFDNUMsT0FBT0Q7UUFDVCxTQUFVO1lBQ1IsbUJBQW1CO1lBQ25CSix1REFBU0EsQ0FBQ2EsVUFBVTtRQUN0QjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxhQUFhQyxlQUFtRDtRQUM5RCxNQUFNVixXQUFXLE1BQU1KLHVEQUFTQSxDQUFDSyxJQUFJLENBQWU7UUFFcEQsSUFBSUQsU0FBU0UsT0FBTyxJQUFJRixTQUFTRCxJQUFJLEVBQUU7WUFDckNILHVEQUFTQSxDQUFDTyxRQUFRLENBQUNILFNBQVNELElBQUksQ0FBQ0ssS0FBSztZQUN0Q1IsdURBQVNBLENBQUNTLE9BQU8sQ0FBQ0wsU0FBU0QsSUFBSSxDQUFDTyxJQUFJO1FBQ3RDO1FBRUEsT0FBT047SUFDVDtJQUVBOztHQUVDLEdBQ0QsYUFBYVcsaUJBQTZDO1FBQ3hELE1BQU1YLFdBQVcsTUFBTUosdURBQVNBLENBQUNnQixHQUFHLENBQU87UUFFM0MsSUFBSVosU0FBU0UsT0FBTyxJQUFJRixTQUFTRCxJQUFJLEVBQUU7WUFDckNILHVEQUFTQSxDQUFDUyxPQUFPLENBQUNMLFNBQVNELElBQUk7UUFDakM7UUFFQSxPQUFPQztJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhYSxjQUFjZCxJQUFtQixFQUE4QjtRQUMxRSxNQUFNQyxXQUFXLE1BQU1KLHVEQUFTQSxDQUFDa0IsR0FBRyxDQUFPLGlCQUFpQmY7UUFFNUQsSUFBSUMsU0FBU0UsT0FBTyxJQUFJRixTQUFTRCxJQUFJLEVBQUU7WUFDckNILHVEQUFTQSxDQUFDUyxPQUFPLENBQUNMLFNBQVNELElBQUk7UUFDakM7UUFFQSxPQUFPQztJQUNUO0lBRUE7O0dBRUMsR0FDRCxhQUFhZSxlQUFlaEIsSUFBMkIsRUFBOEI7UUFDbkYsT0FBT0gsdURBQVNBLENBQUNrQixHQUFHLENBQU8seUJBQXlCZjtJQUN0RDtJQUVBOztHQUVDLEdBQ0QsYUFBYWlCLGVBQWVqQixJQUEyQixFQUE4QjtRQUNuRixPQUFPSCx1REFBU0EsQ0FBQ0ssSUFBSSxDQUFPLHlCQUF5QkY7SUFDdkQ7SUFFQTs7R0FFQyxHQUNELGFBQWFrQixjQUFjbEIsSUFBMEIsRUFBOEI7UUFDakYsT0FBT0gsdURBQVNBLENBQUNLLElBQUksQ0FBTyx3QkFBd0JGO0lBQ3REO0lBRUE7O0dBRUMsR0FDRCxhQUFhbUIsZ0JBQWtDO1FBQzdDLElBQUk7WUFDRixNQUFNbEIsV0FBVyxNQUFNSix1REFBU0EsQ0FBQ2dCLEdBQUcsQ0FBQztZQUNyQyxPQUFPWixTQUFTRSxPQUFPO1FBQ3pCLEVBQUUsT0FBT2lCLE9BQU87WUFDZCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYUMscUJBQXFEO1FBQ2hFLE9BQU94Qix1REFBU0EsQ0FBQ2dCLEdBQUcsQ0FBVztJQUNqQztJQUVBOztHQUVDLEdBQ0QsYUFBYVMsY0FBY0MsVUFBa0IsRUFBb0I7UUFDL0QsSUFBSTtnQkFFeUJ0QjtZQUQzQixNQUFNQSxXQUFXLE1BQU0sSUFBSSxDQUFDb0Isa0JBQWtCO1lBQzlDLE9BQU9wQixTQUFTRSxPQUFPLE1BQUlGLGlCQUFBQSxTQUFTRCxJQUFJLGNBQWJDLHFDQUFBQSxlQUFldUIsUUFBUSxDQUFDRCxnQkFBZTtRQUNwRSxFQUFFLE9BQU9ILE9BQU87WUFDZCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYUssZUFBK0M7UUFDMUQsT0FBTzVCLHVEQUFTQSxDQUFDZ0IsR0FBRyxDQUFXO0lBQ2pDO0lBRUE7O0dBRUMsR0FDRCxhQUFhYSxRQUFRQyxJQUFZLEVBQW9CO1FBQ25ELElBQUk7Z0JBRXlCMUI7WUFEM0IsTUFBTUEsV0FBVyxNQUFNLElBQUksQ0FBQ3dCLFlBQVk7WUFDeEMsT0FBT3hCLFNBQVNFLE9BQU8sTUFBSUYsaUJBQUFBLFNBQVNELElBQUksY0FBYkMscUNBQUFBLGVBQWV1QixRQUFRLENBQUNHLFVBQVM7UUFDOUQsRUFBRSxPQUFPUCxPQUFPO1lBQ2QsT0FBTztRQUNUO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE9BQU9RLGVBQTRCO1FBQ2pDLE9BQU8vQix1REFBU0EsQ0FBQ2dDLE9BQU87SUFDMUI7SUFFQTs7R0FFQyxHQUNELE9BQU9DLGdCQUErQjtRQUNwQyxPQUFPakMsdURBQVNBLENBQUNrQyxRQUFRO0lBQzNCO0lBRUE7O0dBRUMsR0FDRCxPQUFPQyxrQkFBMkI7UUFDaEMsTUFBTTNCLFFBQVEsSUFBSSxDQUFDeUIsYUFBYTtRQUNoQyxNQUFNdkIsT0FBTyxJQUFJLENBQUNxQixZQUFZO1FBQzlCLE9BQU8sQ0FBQyxDQUFFdkIsQ0FBQUEsU0FBU0UsSUFBRztJQUN4QjtJQUVBOztHQUVDLEdBQ0QsT0FBTzBCLFlBQWtCO1FBQ3ZCcEMsdURBQVNBLENBQUNhLFVBQVU7SUFDdEI7QUFDRjtBQUVBLGlFQUFlWixXQUFXQSxFQUFDIiwic291cmNlcyI6WyJFOlxcN21vdXRoTWlzc2lvblxcdW5pRmlnbWFcXGFkbWluLXN5c3RlbVxcc3JjXFxzZXJ2aWNlc1xcYXV0aC5zZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog6K6k6K+B5pyN5YqhXG4gKiDlpITnkIbnlKjmiLforqTor4Hnm7jlhbPnmoRBUEnosIPnlKhcbiAqL1xuXG5pbXBvcnQgQXBpQ2xpZW50IGZyb20gJ0AvbGliL2FwaS1jbGllbnQnO1xuaW1wb3J0IHsgQXBpUmVzcG9uc2UgfSBmcm9tICdAL2xpYi9hcGktdXRpbHMnO1xuXG4vLyDorqTor4Hnm7jlhbPnmoTnsbvlnovlrprkuYlcbmV4cG9ydCBpbnRlcmZhY2UgTG9naW5SZXF1ZXN0IHtcbiAgZW1haWxPclVzZXJuYW1lOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJSZXF1ZXN0IHtcbiAgdXNlcm5hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgcGFzc3dvcmQ6IHN0cmluZztcbiAgZnVsbF9uYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVzZXIge1xuICBpZDogbnVtYmVyO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBmdWxsX25hbWU/OiBzdHJpbmc7XG4gIHJvbGU6IHN0cmluZztcbiAgYXZhdGFyX3VybD86IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICB1cGRhdGVkX2F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aFJlc3BvbnNlIHtcbiAgdXNlcjogVXNlcjtcbiAgdG9rZW46IHN0cmluZztcbiAgZXhwaXJlc19pbjogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENoYW5nZVBhc3N3b3JkUmVxdWVzdCB7XG4gIGN1cnJlbnRQYXNzd29yZDogc3RyaW5nO1xuICBuZXdQYXNzd29yZDogc3RyaW5nO1xuICBjb25maXJtUGFzc3dvcmQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBGb3Jnb3RQYXNzd29yZFJlcXVlc3Qge1xuICBlbWFpbDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFJlc2V0UGFzc3dvcmRSZXF1ZXN0IHtcbiAgdG9rZW46IHN0cmluZztcbiAgcGFzc3dvcmQ6IHN0cmluZztcbiAgY29uZmlybVBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbi8vIOiupOivgeacjeWKoeexu1xuZXhwb3J0IGNsYXNzIEF1dGhTZXJ2aWNlIHtcbiAgLyoqXG4gICAqIOeUqOaIt+eZu+W9lVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGxvZ2luKGRhdGE6IExvZ2luUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8QXV0aFJlc3BvbnNlPj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgQXBpQ2xpZW50LnBvc3Q8QXV0aFJlc3BvbnNlPignL2F1dGgvbG9naW4nLCBkYXRhKTtcbiAgICBcbiAgICAvLyDnmbvlvZXmiJDlip/lkI7kv53lrZjnlKjmiLfkv6Hmga/lkox0b2tlblxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgIEFwaUNsaWVudC5zZXRUb2tlbihyZXNwb25zZS5kYXRhLnRva2VuKTtcbiAgICAgIEFwaUNsaWVudC5zZXRVc2VyKHJlc3BvbnNlLmRhdGEudXNlcik7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfVxuXG4gIC8qKlxuICAgKiDnlKjmiLfms6jlhoxcbiAgICovXG4gIHN0YXRpYyBhc3luYyByZWdpc3RlcihkYXRhOiBSZWdpc3RlclJlcXVlc3QpOiBQcm9taXNlPEFwaVJlc3BvbnNlPEF1dGhSZXNwb25zZT4+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFwaUNsaWVudC5wb3N0PEF1dGhSZXNwb25zZT4oJy9hdXRoL3JlZ2lzdGVyJywgZGF0YSk7XG4gICAgXG4gICAgLy8g5rOo5YaM5oiQ5Yqf5ZCO5L+d5a2Y55So5oi35L+h5oGv5ZKMdG9rZW5cbiAgICBpZiAocmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhKSB7XG4gICAgICBBcGlDbGllbnQuc2V0VG9rZW4ocmVzcG9uc2UuZGF0YS50b2tlbik7XG4gICAgICBBcGlDbGllbnQuc2V0VXNlcihyZXNwb25zZS5kYXRhLnVzZXIpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gcmVzcG9uc2U7XG4gIH1cblxuICAvKipcbiAgICog55So5oi355m75Ye6XG4gICAqL1xuICBzdGF0aWMgYXN5bmMgbG9nb3V0KCk6IFByb21pc2U8QXBpUmVzcG9uc2U8dm9pZD4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlDbGllbnQucG9zdDx2b2lkPignL2F1dGgvbG9nb3V0Jyk7XG4gICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIC8vIOaXoOiuuuivt+axguaYr+WQpuaIkOWKn++8jOmDvea4hemZpOacrOWcsOWtmOWCqFxuICAgICAgQXBpQ2xpZW50LmNsZWFyVG9rZW4oKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5Yi35pawdG9rZW5cbiAgICovXG4gIHN0YXRpYyBhc3luYyByZWZyZXNoVG9rZW4oKTogUHJvbWlzZTxBcGlSZXNwb25zZTxBdXRoUmVzcG9uc2U+PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlDbGllbnQucG9zdDxBdXRoUmVzcG9uc2U+KCcvYXV0aC9yZWZyZXNoJyk7XG4gICAgXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgQXBpQ2xpZW50LnNldFRva2VuKHJlc3BvbnNlLmRhdGEudG9rZW4pO1xuICAgICAgQXBpQ2xpZW50LnNldFVzZXIocmVzcG9uc2UuZGF0YS51c2VyKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluW9k+WJjeeUqOaIt+S/oeaBr1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldEN1cnJlbnRVc2VyKCk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFwaUNsaWVudC5nZXQ8VXNlcj4oJy9hdXRoL21lJyk7XG4gICAgXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgQXBpQ2xpZW50LnNldFVzZXIocmVzcG9uc2UuZGF0YSk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfVxuXG4gIC8qKlxuICAgKiDmm7TmlrDnlKjmiLfotYTmlplcbiAgICovXG4gIHN0YXRpYyBhc3luYyB1cGRhdGVQcm9maWxlKGRhdGE6IFBhcnRpYWw8VXNlcj4pOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXI+PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlDbGllbnQucHV0PFVzZXI+KCcvYXV0aC9wcm9maWxlJywgZGF0YSk7XG4gICAgXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgQXBpQ2xpZW50LnNldFVzZXIocmVzcG9uc2UuZGF0YSk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiByZXNwb25zZTtcbiAgfVxuXG4gIC8qKlxuICAgKiDkv67mlLnlr4bnoIFcbiAgICovXG4gIHN0YXRpYyBhc3luYyBjaGFuZ2VQYXNzd29yZChkYXRhOiBDaGFuZ2VQYXNzd29yZFJlcXVlc3QpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHZvaWQ+PiB7XG4gICAgcmV0dXJuIEFwaUNsaWVudC5wdXQ8dm9pZD4oJy9hdXRoL2NoYW5nZS1wYXNzd29yZCcsIGRhdGEpO1xuICB9XG5cbiAgLyoqXG4gICAqIOW/mOiusOWvhueggVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGZvcmdvdFBhc3N3b3JkKGRhdGE6IEZvcmdvdFBhc3N3b3JkUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8dm9pZD4+IHtcbiAgICByZXR1cm4gQXBpQ2xpZW50LnBvc3Q8dm9pZD4oJy9hdXRoL2ZvcmdvdC1wYXNzd29yZCcsIGRhdGEpO1xuICB9XG5cbiAgLyoqXG4gICAqIOmHjee9ruWvhueggVxuICAgKi9cbiAgc3RhdGljIGFzeW5jIHJlc2V0UGFzc3dvcmQoZGF0YTogUmVzZXRQYXNzd29yZFJlcXVlc3QpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHZvaWQ+PiB7XG4gICAgcmV0dXJuIEFwaUNsaWVudC5wb3N0PHZvaWQ+KCcvYXV0aC9yZXNldC1wYXNzd29yZCcsIGRhdGEpO1xuICB9XG5cbiAgLyoqXG4gICAqIOmqjOivgXRva2Vu5piv5ZCm5pyJ5pWIXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgdmFsaWRhdGVUb2tlbigpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlDbGllbnQuZ2V0KCcvYXV0aC92YWxpZGF0ZScpO1xuICAgICAgcmV0dXJuIHJlc3BvbnNlLnN1Y2Nlc3M7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog6I635Y+W55So5oi35p2D6ZmQXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgZ2V0VXNlclBlcm1pc3Npb25zKCk6IFByb21pc2U8QXBpUmVzcG9uc2U8c3RyaW5nW10+PiB7XG4gICAgcmV0dXJuIEFwaUNsaWVudC5nZXQ8c3RyaW5nW10+KCcvYXV0aC9wZXJtaXNzaW9ucycpO1xuICB9XG5cbiAgLyoqXG4gICAqIOajgOafpeeUqOaIt+aYr+WQpuacieeJueWumuadg+mZkFxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGhhc1Blcm1pc3Npb24ocGVybWlzc2lvbjogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRVc2VyUGVybWlzc2lvbnMoKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGE/LmluY2x1ZGVzKHBlcm1pc3Npb24pIHx8IGZhbHNlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPlueUqOaIt+inkuiJslxuICAgKi9cbiAgc3RhdGljIGFzeW5jIGdldFVzZXJSb2xlcygpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHN0cmluZ1tdPj4ge1xuICAgIHJldHVybiBBcGlDbGllbnQuZ2V0PHN0cmluZ1tdPignL2F1dGgvcm9sZXMnKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDmo4Dmn6XnlKjmiLfmmK/lkKbmnInnibnlrprop5LoibJcbiAgICovXG4gIHN0YXRpYyBhc3luYyBoYXNSb2xlKHJvbGU6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZ2V0VXNlclJvbGVzKCk7XG4gICAgICByZXR1cm4gcmVzcG9uc2Uuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhPy5pbmNsdWRlcyhyb2xlKSB8fCBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDojrflj5bmnKzlnLDlrZjlgqjnmoTnlKjmiLfkv6Hmga9cbiAgICovXG4gIHN0YXRpYyBnZXRMb2NhbFVzZXIoKTogVXNlciB8IG51bGwge1xuICAgIHJldHVybiBBcGlDbGllbnQuZ2V0VXNlcigpO1xuICB9XG5cbiAgLyoqXG4gICAqIOiOt+WPluacrOWcsOWtmOWCqOeahHRva2VuXG4gICAqL1xuICBzdGF0aWMgZ2V0TG9jYWxUb2tlbigpOiBzdHJpbmcgfCBudWxsIHtcbiAgICByZXR1cm4gQXBpQ2xpZW50LmdldFRva2VuKCk7XG4gIH1cblxuICAvKipcbiAgICog5qOA5p+l55So5oi35piv5ZCm5bey55m75b2VXG4gICAqL1xuICBzdGF0aWMgaXNBdXRoZW50aWNhdGVkKCk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IHRva2VuID0gdGhpcy5nZXRMb2NhbFRva2VuKCk7XG4gICAgY29uc3QgdXNlciA9IHRoaXMuZ2V0TG9jYWxVc2VyKCk7XG4gICAgcmV0dXJuICEhKHRva2VuICYmIHVzZXIpO1xuICB9XG5cbiAgLyoqXG4gICAqIOa4hemZpOiupOivgeS/oeaBr1xuICAgKi9cbiAgc3RhdGljIGNsZWFyQXV0aCgpOiB2b2lkIHtcbiAgICBBcGlDbGllbnQuY2xlYXJUb2tlbigpO1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IEF1dGhTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbIkFwaUNsaWVudCIsIkF1dGhTZXJ2aWNlIiwibG9naW4iLCJkYXRhIiwicmVzcG9uc2UiLCJwb3N0Iiwic3VjY2VzcyIsInNldFRva2VuIiwidG9rZW4iLCJzZXRVc2VyIiwidXNlciIsInJlZ2lzdGVyIiwibG9nb3V0IiwiY2xlYXJUb2tlbiIsInJlZnJlc2hUb2tlbiIsImdldEN1cnJlbnRVc2VyIiwiZ2V0IiwidXBkYXRlUHJvZmlsZSIsInB1dCIsImNoYW5nZVBhc3N3b3JkIiwiZm9yZ290UGFzc3dvcmQiLCJyZXNldFBhc3N3b3JkIiwidmFsaWRhdGVUb2tlbiIsImVycm9yIiwiZ2V0VXNlclBlcm1pc3Npb25zIiwiaGFzUGVybWlzc2lvbiIsInBlcm1pc3Npb24iLCJpbmNsdWRlcyIsImdldFVzZXJSb2xlcyIsImhhc1JvbGUiLCJyb2xlIiwiZ2V0TG9jYWxVc2VyIiwiZ2V0VXNlciIsImdldExvY2FsVG9rZW4iLCJnZXRUb2tlbiIsImlzQXV0aGVudGljYXRlZCIsImNsZWFyQXV0aCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/auth.service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/index.ts":
/*!*******************************!*\
  !*** ./src/services/index.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API: () => (/* binding */ API),\n/* harmony export */   AuthService: () => (/* reexport safe */ _auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService),\n/* harmony export */   ServiceCache: () => (/* binding */ ServiceCache),\n/* harmony export */   ServiceErrorHandler: () => (/* binding */ ServiceErrorHandler),\n/* harmony export */   ServiceFactory: () => (/* binding */ ServiceFactory),\n/* harmony export */   ServiceStatus: () => (/* binding */ ServiceStatus),\n/* harmony export */   Services: () => (/* binding */ Services),\n/* harmony export */   UserService: () => (/* reexport safe */ _user_service__WEBPACK_IMPORTED_MODULE_1__.UserService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth.service */ \"(app-pages-browser)/./src/services/auth.service.ts\");\n/* harmony import */ var _user_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./user.service */ \"(app-pages-browser)/./src/services/user.service.ts\");\n/**\n * 服务模块统一导出\n * 提供所有API服务的统一入口\n */ // 导出服务类\n\n\n// 导入所有服务\n\n\n// 创建统一的API服务对象\nconst API = {\n    auth: _auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService,\n    user: _user_service__WEBPACK_IMPORTED_MODULE_1__.UserService\n};\n// 默认导出\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (API);\n// 服务工厂函数，用于创建带有通用配置的服务实例\nclass ServiceFactory {\n    /**\n   * 设置全局服务配置\n   */ static setGlobalConfig(config) {\n        Object.assign(this.baseConfig, config);\n    }\n    /**\n   * 获取全局服务配置\n   */ static getGlobalConfig() {\n        return {\n            ...this.baseConfig\n        };\n    }\n    /**\n   * 创建带有自定义配置的服务实例\n   */ static createService(serviceName, config) {\n        // 这里可以根据需要对服务进行配置\n        // 目前返回原始服务，后续可以扩展为代理对象来应用配置\n        if (config) {\n        // 配置逻辑可以在这里实现\n        }\n        return API[serviceName];\n    }\n}\nServiceFactory.baseConfig = {\n    timeout: 10000,\n    retryCount: 3,\n    showError: true\n};\n// 便捷的服务访问器\nconst Services = {\n    /**\n   * 认证服务\n   */ get auth () {\n        return _auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService;\n    },\n    /**\n   * 用户管理服务\n   */ get user () {\n        return _user_service__WEBPACK_IMPORTED_MODULE_1__.UserService;\n    },\n    /**\n   * 获取所有服务\n   */ getAll () {\n        return API;\n    },\n    /**\n   * 检查服务是否可用\n   */ async healthCheck () {\n        try {\n            // 这里可以添加健康检查逻辑\n            // 例如调用一个健康检查端点\n            return true;\n        } catch (error) {\n            console.error('Service health check failed:', error);\n            return false;\n        }\n    }\n};\n// 服务状态管理\nclass ServiceStatus {\n    /**\n   * 设置服务状态\n   */ static setStatus(service, status) {\n        this.status[service] = status;\n    }\n    /**\n   * 获取服务状态\n   */ static getStatus(service) {\n        return this.status[service] || 'unknown';\n    }\n    /**\n   * 获取所有服务状态\n   */ static getAllStatus() {\n        return {\n            ...this.status\n        };\n    }\n    /**\n   * 重置所有服务状态\n   */ static reset() {\n        this.status = {};\n    }\n}\nServiceStatus.status = {};\n// 服务错误处理器\nclass ServiceErrorHandler {\n    /**\n   * 注册错误处理器\n   */ static register(errorType, handler) {\n        this.handlers[errorType] = handler;\n    }\n    /**\n   * 处理错误\n   */ static handle(errorType, error) {\n        const handler = this.handlers[errorType];\n        if (handler) {\n            handler(error);\n        } else {\n            console.error(\"Unhandled service error (\".concat(errorType, \"):\"), error);\n        }\n    }\n    /**\n   * 移除错误处理器\n   */ static unregister(errorType) {\n        delete this.handlers[errorType];\n    }\n    /**\n   * 清除所有错误处理器\n   */ static clear() {\n        this.handlers = {};\n    }\n}\nServiceErrorHandler.handlers = {};\n// 服务缓存管理\nclass ServiceCache {\n    /**\n   * 设置缓存\n   */ static set(key, data) {\n        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 300000;\n        this.cache.set(key, {\n            data,\n            timestamp: Date.now(),\n            ttl\n        });\n    }\n    /**\n   * 获取缓存\n   */ static get(key) {\n        const item = this.cache.get(key);\n        if (!item) return null;\n        const now = Date.now();\n        if (now - item.timestamp > item.ttl) {\n            this.cache.delete(key);\n            return null;\n        }\n        return item.data;\n    }\n    /**\n   * 删除缓存\n   */ static delete(key) {\n        return this.cache.delete(key);\n    }\n    /**\n   * 清除所有缓存\n   */ static clear() {\n        this.cache.clear();\n    }\n    /**\n   * 清除过期缓存\n   */ static clearExpired() {\n        const now = Date.now();\n        for (const [key, item] of this.cache.entries()){\n            if (now - item.timestamp > item.ttl) {\n                this.cache.delete(key);\n            }\n        }\n    }\n}\nServiceCache.cache = new Map();\n// 定期清理过期缓存\nif (true) {\n    setInterval(()=>{\n        ServiceCache.clearExpired();\n    }, 60000); // 每分钟清理一次\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/user.service.ts":
/*!**************************************!*\
  !*** ./src/services/user.service.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserService: () => (/* binding */ UserService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/**\n * 用户管理服务\n * 处理用户管理相关的API调用\n */ \n// 用户管理服务类\nclass UserService {\n    /**\n   * 获取用户列表\n   */ static async getUsers(params) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users', params);\n    }\n    /**\n   * 根据ID获取用户详情\n   */ static async getUserById(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/users/\".concat(id));\n    }\n    /**\n   * 创建新用户\n   */ static async createUser(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/users', data);\n    }\n    /**\n   * 更新用户信息\n   */ static async updateUser(id, data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/users/\".concat(id), data);\n    }\n    /**\n   * 删除用户\n   */ static async deleteUser(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/users/\".concat(id));\n    }\n    /**\n   * 批量删除用户\n   */ static async deleteUsers(ids) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/users/batch-delete', {\n            ids\n        });\n    }\n    /**\n   * 激活用户\n   */ static async activateUser(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/users/\".concat(id, \"/activate\"));\n    }\n    /**\n   * 停用用户\n   */ static async deactivateUser(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/users/\".concat(id, \"/deactivate\"));\n    }\n    /**\n   * 重置用户密码\n   */ static async resetUserPassword(id, newPassword) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/users/\".concat(id, \"/reset-password\"), {\n            password: newPassword\n        });\n    }\n    /**\n   * 更改用户角色\n   */ static async changeUserRole(id, role) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/users/\".concat(id, \"/role\"), {\n            role\n        });\n    }\n    /**\n   * 获取用户统计信息\n   */ static async getUserStats() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users/stats');\n    }\n    /**\n   * 搜索用户\n   */ static async searchUsers(query, params) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users/search', {\n            search: query,\n            ...params\n        });\n    }\n    /**\n   * 导出用户数据\n   */ static async exportUsers(params) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users/export', params);\n    }\n    /**\n   * 导入用户数据\n   */ static async importUsers(file) {\n        const formData = new FormData();\n        formData.append('file', file);\n        // 使用原生fetch处理文件上传\n        const token = _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getToken();\n        const response = await fetch('/api/users/import', {\n            method: 'POST',\n            headers: {\n                'Authorization': token ? \"Bearer \".concat(token) : ''\n            },\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP \".concat(response.status));\n        }\n        return response.json();\n    }\n    /**\n   * 获取用户活动日志\n   */ static async getUserActivityLog(id, params) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/users/\".concat(id, \"/activity\"), params);\n    }\n    /**\n   * 获取用户会话信息\n   */ static async getUserSessions(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/users/\".concat(id, \"/sessions\"));\n    }\n    /**\n   * 终止用户会话\n   */ static async terminateUserSession(userId, sessionId) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/users/\".concat(userId, \"/sessions/\").concat(sessionId));\n    }\n    /**\n   * 终止用户所有会话\n   */ static async terminateAllUserSessions(userId) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/users/\".concat(userId, \"/sessions\"));\n    }\n    /**\n   * 获取用户权限\n   */ static async getUserPermissions(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/users/\".concat(id, \"/permissions\"));\n    }\n    /**\n   * 设置用户权限\n   */ static async setUserPermissions(id, permissions) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/users/\".concat(id, \"/permissions\"), {\n            permissions\n        });\n    }\n    /**\n   * 发送用户通知\n   */ static async sendNotificationToUser(id, notification) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/users/\".concat(id, \"/notifications\"), notification);\n    }\n    /**\n   * 验证用户名是否可用\n   */ static async checkUsernameAvailability(username) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users/check-username', {\n            username\n        });\n    }\n    /**\n   * 验证邮箱是否可用\n   */ static async checkEmailAvailability(email) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/users/check-email', {\n            email\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/user.service.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C7mouthMission%5C%5CuniFigma%5C%5Cadmin-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);