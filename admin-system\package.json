{"name": "admin-system", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env.development -- next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "db:reset": "node scripts/init-db.js"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dotenv": "^17.2.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-cli": "^9.0.0", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}