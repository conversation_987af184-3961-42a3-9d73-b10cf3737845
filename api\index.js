/**
 * API接口定义
 * 统一管理所有API接口，实现接口分离和复用
 */

import api from '../utils/api.js';

// 用户认证相关API
export const authAPI = {
  // 用户登录
  login(data) {
    return api.post('/auth/login', data);
  },

  // 用户注册
  register(data) {
    return api.post('/auth/register', data);
  },

  // 刷新token
  refreshToken() {
    return api.post('/auth/refresh');
  },

  // 退出登录
  logout() {
    return api.post('/auth/logout');
  },

  // 获取用户信息
  getUserInfo() {
    return api.get('/auth/me');
  },

  // 修改密码
  changePassword(data) {
    return api.put('/auth/change-password', data);
  },

  // 忘记密码
  forgotPassword(data) {
    return api.post('/auth/forgot-password', data);
  },

  // 重置密码
  resetPassword(data) {
    return api.post('/auth/reset-password', data);
  }
};

// 用户相关API
export const userAPI = {
  // 获取用户列表
  getUsers(params = {}) {
    return api.get('/users', params);
  },

  // 获取用户详情
  getUserById(id) {
    return api.get(`/users/${id}`);
  },

  // 创建用户
  createUser(data) {
    return api.post('/users', data);
  },

  // 更新用户信息
  updateUser(id, data) {
    return api.put(`/users/${id}`, data);
  },

  // 删除用户
  deleteUser(id) {
    return api.delete(`/users/${id}`);
  },

  // 更新用户头像
  updateAvatar(data) {
    return api.post('/users/avatar', data);
  },

  // 更新用户资料
  updateProfile(data) {
    return api.put('/users/profile', data);
  }
};

// 文件上传相关API
export const uploadAPI = {
  // 上传图片
  uploadImage(filePath, formData = {}) {
    return new Promise((resolve, reject) => {
      const token = api.TokenManager.getToken();
      
      uni.uploadFile({
        url: api.config.PROD_BASE_URL + '/upload/image',
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(new Error(data.error || '上传失败'));
            }
          } catch (e) {
            reject(new Error('响应解析失败'));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  // 上传文件
  uploadFile(filePath, formData = {}) {
    return new Promise((resolve, reject) => {
      const token = api.TokenManager.getToken();
      
      uni.uploadFile({
        url: api.config.PROD_BASE_URL + '/upload/file',
        filePath: filePath,
        name: 'file',
        formData: formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data);
            } else {
              reject(new Error(data.error || '上传失败'));
            }
          } catch (e) {
            reject(new Error('响应解析失败'));
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }
};

// 内容相关API（示例）
export const contentAPI = {
  // 获取内容列表
  getContentList(params = {}) {
    return api.get('/content', params);
  },

  // 获取内容详情
  getContentById(id) {
    return api.get(`/content/${id}`);
  },

  // 创建内容
  createContent(data) {
    return api.post('/content', data);
  },

  // 更新内容
  updateContent(id, data) {
    return api.put(`/content/${id}`, data);
  },

  // 删除内容
  deleteContent(id) {
    return api.delete(`/content/${id}`);
  },

  // 搜索内容
  searchContent(keyword, params = {}) {
    return api.get('/content/search', { keyword, ...params });
  }
};

// 系统配置相关API
export const systemAPI = {
  // 获取系统配置
  getConfig() {
    return api.get('/system/config');
  },

  // 获取版本信息
  getVersion() {
    return api.get('/system/version');
  },

  // 意见反馈
  feedback(data) {
    return api.post('/system/feedback', data);
  },

  // 获取公告
  getAnnouncements(params = {}) {
    return api.get('/system/announcements', params);
  }
};

// 统计相关API
export const analyticsAPI = {
  // 获取用户统计
  getUserStats(params = {}) {
    return api.get('/analytics/users', params);
  },

  // 获取内容统计
  getContentStats(params = {}) {
    return api.get('/analytics/content', params);
  },

  // 获取访问统计
  getVisitStats(params = {}) {
    return api.get('/analytics/visits', params);
  }
};

// 通知相关API
export const notificationAPI = {
  // 获取通知列表
  getNotifications(params = {}) {
    return api.get('/notifications', params);
  },

  // 标记通知为已读
  markAsRead(id) {
    return api.put(`/notifications/${id}/read`);
  },

  // 标记所有通知为已读
  markAllAsRead() {
    return api.put('/notifications/read-all');
  },

  // 删除通知
  deleteNotification(id) {
    return api.delete(`/notifications/${id}`);
  }
};

// 导出所有API
export default {
  auth: authAPI,
  user: userAPI,
  upload: uploadAPI,
  content: contentAPI,
  system: systemAPI,
  analytics: analyticsAPI,
  notification: notificationAPI,
  
  // 直接导出api工具
  api
};

// 也可以单独导出各个模块
export {
  authAPI,
  userAPI,
  uploadAPI,
  contentAPI,
  systemAPI,
  analyticsAPI,
  notificationAPI
};
