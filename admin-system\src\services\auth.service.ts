/**
 * 认证服务
 * 处理用户认证相关的API调用
 */

import ApiClient from '@/lib/api-client';
import { ApiResponse } from '@/lib/api-utils';

// 认证相关的类型定义
export interface LoginRequest {
  emailOrUsername: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  role: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  expires_in: number;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

// 认证服务类
export class AuthService {
  /**
   * 用户登录
   */
  static async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await ApiClient.post<AuthResponse>('/auth/login', data);
    
    // 登录成功后保存用户信息和token
    if (response.success && response.data) {
      ApiClient.setToken(response.data.token);
      ApiClient.setUser(response.data.user);
    }
    
    return response;
  }

  /**
   * 用户注册
   */
  static async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await ApiClient.post<AuthResponse>('/auth/register', data);
    
    // 注册成功后保存用户信息和token
    if (response.success && response.data) {
      ApiClient.setToken(response.data.token);
      ApiClient.setUser(response.data.user);
    }
    
    return response;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<void>> {
    try {
      const response = await ApiClient.post<void>('/auth/logout');
      return response;
    } finally {
      // 无论请求是否成功，都清除本地存储
      ApiClient.clearToken();
    }
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<ApiResponse<AuthResponse>> {
    const response = await ApiClient.post<AuthResponse>('/auth/refresh');
    
    if (response.success && response.data) {
      ApiClient.setToken(response.data.token);
      ApiClient.setUser(response.data.user);
    }
    
    return response;
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    const response = await ApiClient.get<User>('/auth/me');
    
    if (response.success && response.data) {
      ApiClient.setUser(response.data);
    }
    
    return response;
  }

  /**
   * 更新用户资料
   */
  static async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    const response = await ApiClient.put<User>('/auth/profile', data);
    
    if (response.success && response.data) {
      ApiClient.setUser(response.data);
    }
    
    return response;
  }

  /**
   * 修改密码
   */
  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return ApiClient.put<void>('/auth/change-password', data);
  }

  /**
   * 忘记密码
   */
  static async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<void>> {
    return ApiClient.post<void>('/auth/forgot-password', data);
  }

  /**
   * 重置密码
   */
  static async resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<void>> {
    return ApiClient.post<void>('/auth/reset-password', data);
  }

  /**
   * 验证token是否有效
   */
  static async validateToken(): Promise<boolean> {
    try {
      const response = await ApiClient.get('/auth/validate');
      return response.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取用户权限
   */
  static async getUserPermissions(): Promise<ApiResponse<string[]>> {
    return ApiClient.get<string[]>('/auth/permissions');
  }

  /**
   * 检查用户是否有特定权限
   */
  static async hasPermission(permission: string): Promise<boolean> {
    try {
      const response = await this.getUserPermissions();
      return response.success && response.data?.includes(permission) || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取用户角色
   */
  static async getUserRoles(): Promise<ApiResponse<string[]>> {
    return ApiClient.get<string[]>('/auth/roles');
  }

  /**
   * 检查用户是否有特定角色
   */
  static async hasRole(role: string): Promise<boolean> {
    try {
      const response = await this.getUserRoles();
      return response.success && response.data?.includes(role) || false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取本地存储的用户信息
   */
  static getLocalUser(): User | null {
    return ApiClient.getUser();
  }

  /**
   * 获取本地存储的token
   */
  static getLocalToken(): string | null {
    return ApiClient.getToken();
  }

  /**
   * 检查用户是否已登录
   */
  static isAuthenticated(): boolean {
    const token = this.getLocalToken();
    const user = this.getLocalUser();
    return !!(token && user);
  }

  /**
   * 清除认证信息
   */
  static clearAuth(): void {
    ApiClient.clearToken();
  }
}

export default AuthService;
