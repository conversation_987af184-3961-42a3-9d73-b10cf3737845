[{"E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\analytics\\page.tsx": "1", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\dashboard\\page.tsx": "2", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\layout.tsx": "3", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\settings\\page.tsx": "4", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\users\\page.tsx": "5", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\auth\\login\\route.ts": "6", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\auth\\register\\route.ts": "7", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\mobile\\auth\\login\\route.ts": "8", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\mobile\\user\\profile\\route.ts": "9", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\users\\route.ts": "10", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\users\\[id]\\route.ts": "11", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\layout.tsx": "12", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\login\\page.tsx": "13", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\page.tsx": "14", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\register\\page.tsx": "15", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\admin-layout.tsx": "16", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\app-sidebar.tsx": "17", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\mode-toggle.tsx": "18", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\theme-provider.tsx": "19", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\avatar.tsx": "20", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\badge.tsx": "21", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\button.tsx": "22", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\card.tsx": "23", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\dropdown-menu.tsx": "24", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\input.tsx": "25", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\label.tsx": "26", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\select.tsx": "27", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\separator.tsx": "28", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\sheet.tsx": "29", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\sidebar.tsx": "30", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\skeleton.tsx": "31", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\switch.tsx": "32", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\table.tsx": "33", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\tooltip.tsx": "34", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\contexts\\auth-context.tsx": "35", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\hooks\\use-mobile.tsx": "36", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\api-client.ts": "37", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\api-utils.ts": "38", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\auth.ts": "39", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\client-detection.ts": "40", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\database.ts": "41", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\utils.ts": "42", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\auth.service.ts": "43", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\index.ts": "44", "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\user.service.ts": "45"}, {"size": 9922, "mtime": 1753536662285, "results": "46", "hashOfConfig": "47"}, {"size": 8208, "mtime": 1753536580335, "results": "48", "hashOfConfig": "47"}, {"size": 215, "mtime": 1753536542808, "results": "49", "hashOfConfig": "47"}, {"size": 8853, "mtime": 1753536897272, "results": "50", "hashOfConfig": "47"}, {"size": 8713, "mtime": 1753536619765, "results": "51", "hashOfConfig": "47"}, {"size": 2476, "mtime": 1753532132263, "results": "52", "hashOfConfig": "47"}, {"size": 3908, "mtime": 1753530192159, "results": "53", "hashOfConfig": "47"}, {"size": 5140, "mtime": 1753538876896, "results": "54", "hashOfConfig": "47"}, {"size": 6250, "mtime": 1753532702204, "results": "55", "hashOfConfig": "47"}, {"size": 3123, "mtime": 1753532634465, "results": "56", "hashOfConfig": "47"}, {"size": 5514, "mtime": 1753538742407, "results": "57", "hashOfConfig": "47"}, {"size": 1094, "mtime": 1753532058683, "results": "58", "hashOfConfig": "47"}, {"size": 5826, "mtime": 1753535050203, "results": "59", "hashOfConfig": "47"}, {"size": 704, "mtime": 1753530335117, "results": "60", "hashOfConfig": "47"}, {"size": 10389, "mtime": 1753535795675, "results": "61", "hashOfConfig": "47"}, {"size": 5609, "mtime": 1753536851085, "results": "62", "hashOfConfig": "47"}, {"size": 10669, "mtime": 1753536801135, "results": "63", "hashOfConfig": "47"}, {"size": 1236, "mtime": 1753530258377, "results": "64", "hashOfConfig": "47"}, {"size": 295, "mtime": 1753530245333, "results": "65", "hashOfConfig": "47"}, {"size": 1097, "mtime": 1753530029216, "results": "66", "hashOfConfig": "47"}, {"size": 1631, "mtime": 1753530029222, "results": "67", "hashOfConfig": "47"}, {"size": 2123, "mtime": 1753530029133, "results": "68", "hashOfConfig": "47"}, {"size": 1989, "mtime": 1753530029152, "results": "69", "hashOfConfig": "47"}, {"size": 8284, "mtime": 1753530029198, "results": "70", "hashOfConfig": "47"}, {"size": 967, "mtime": 1753530029157, "results": "71", "hashOfConfig": "47"}, {"size": 611, "mtime": 1753530029163, "results": "72", "hashOfConfig": "47"}, {"size": 5559, "mtime": 1753536709412, "results": "73", "hashOfConfig": "47"}, {"size": 760, "mtime": 1753536214120, "results": "74", "hashOfConfig": "47"}, {"size": 4241, "mtime": 1753536236527, "results": "75", "hashOfConfig": "47"}, {"size": 23368, "mtime": 1753536373795, "results": "76", "hashOfConfig": "47"}, {"size": 261, "mtime": 1753536246050, "results": "77", "hashOfConfig": "47"}, {"size": 1143, "mtime": 1753536914396, "results": "78", "hashOfConfig": "47"}, {"size": 2448, "mtime": 1753530029178, "results": "79", "hashOfConfig": "47"}, {"size": 1149, "mtime": 1753536257455, "results": "80", "hashOfConfig": "47"}, {"size": 5634, "mtime": 1753538965369, "results": "81", "hashOfConfig": "47"}, {"size": 579, "mtime": 1753536267752, "results": "82", "hashOfConfig": "47"}, {"size": 7183, "mtime": 1753539015672, "results": "83", "hashOfConfig": "47"}, {"size": 9198, "mtime": 1753532595758, "results": "84", "hashOfConfig": "47"}, {"size": 6914, "mtime": 1753530119750, "results": "85", "hashOfConfig": "47"}, {"size": 7842, "mtime": 1753532545337, "results": "86", "hashOfConfig": "47"}, {"size": 4204, "mtime": 1753539323669, "results": "87", "hashOfConfig": "47"}, {"size": 166, "mtime": 1753530010945, "results": "88", "hashOfConfig": "47"}, {"size": 5403, "mtime": 1753532351715, "results": "89", "hashOfConfig": "47"}, {"size": 5129, "mtime": 1753539227944, "results": "90", "hashOfConfig": "47"}, {"size": 6700, "mtime": 1753539248685, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "19rmmy", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\analytics\\page.tsx", ["227", "228"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\dashboard\\page.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\layout.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\settings\\page.tsx", ["229", "230"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\(admin)\\users\\page.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\auth\\login\\route.ts", ["231"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\auth\\register\\route.ts", ["232"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\mobile\\auth\\login\\route.ts", ["233", "234"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\mobile\\user\\profile\\route.ts", ["235", "236", "237", "238"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\users\\route.ts", ["239", "240", "241", "242", "243", "244", "245"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\api\\users\\[id]\\route.ts", ["246", "247", "248", "249"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\layout.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\login\\page.tsx", ["250"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\page.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\app\\register\\page.tsx", ["251"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\admin-layout.tsx", ["252"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\app-sidebar.tsx", ["253", "254", "255", "256"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\mode-toggle.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\theme-provider.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\avatar.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\badge.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\button.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\card.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\dropdown-menu.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\input.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\label.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\select.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\separator.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\sheet.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\sidebar.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\skeleton.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\switch.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\table.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\components\\ui\\tooltip.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\contexts\\auth-context.tsx", ["257", "258", "259", "260", "261", "262"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\hooks\\use-mobile.tsx", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\api-client.ts", ["263", "264", "265", "266", "267", "268", "269", "270", "271", "272", "273", "274", "275", "276", "277"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\api-utils.ts", ["278", "279", "280", "281", "282", "283", "284", "285", "286"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\auth.ts", ["287", "288", "289", "290", "291"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\client-detection.ts", ["292"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\database.ts", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\lib\\utils.ts", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\auth.service.ts", ["293", "294", "295"], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\index.ts", [], [], "E:\\7mouthMission\\uniFigma\\admin-system\\src\\services\\user.service.ts", [], [], {"ruleId": "296", "severity": 1, "message": "297", "line": 7, "column": 10, "nodeType": null, "messageId": "298", "endLine": 7, "endColumn": 15}, {"ruleId": "296", "severity": 1, "message": "299", "line": 16, "column": 3, "nodeType": null, "messageId": "298", "endLine": 16, "endColumn": 12}, {"ruleId": "296", "severity": 1, "message": "300", "line": 17, "column": 28, "nodeType": null, "messageId": "298", "endLine": 17, "endColumn": 36}, {"ruleId": "296", "severity": 1, "message": "301", "line": 17, "column": 53, "nodeType": null, "messageId": "298", "endLine": 17, "endColumn": 58}, {"ruleId": "296", "severity": 1, "message": "302", "line": 24, "column": 22, "nodeType": null, "messageId": "298", "endLine": 24, "endColumn": 28}, {"ruleId": "296", "severity": 1, "message": "302", "line": 24, "column": 22, "nodeType": null, "messageId": "298", "endLine": 24, "endColumn": 28}, {"ruleId": "296", "severity": 1, "message": "303", "line": 17, "column": 26, "nodeType": null, "messageId": "298", "endLine": 17, "endColumn": 36}, {"ruleId": "296", "severity": 1, "message": "302", "line": 40, "column": 22, "nodeType": null, "messageId": "298", "endLine": 40, "endColumn": 28}, {"ruleId": "296", "severity": 1, "message": "304", "line": 13, "column": 3, "nodeType": null, "messageId": "298", "endLine": 13, "endColumn": 13}, {"ruleId": "296", "severity": 1, "message": "302", "line": 81, "column": 24, "nodeType": null, "messageId": "298", "endLine": 81, "endColumn": 30}, {"ruleId": "305", "severity": 2, "message": "306", "line": 107, "column": 25, "nodeType": "307", "messageId": "308", "endLine": 107, "endColumn": 28, "suggestions": "309"}, {"ruleId": "296", "severity": 1, "message": "310", "line": 181, "column": 13, "nodeType": null, "messageId": "298", "endLine": 181, "endColumn": 24}, {"ruleId": "296", "severity": 1, "message": "311", "line": 4, "column": 3, "nodeType": null, "messageId": "298", "endLine": 4, "endColumn": 11}, {"ruleId": "296", "severity": 1, "message": "312", "line": 5, "column": 3, "nodeType": null, "messageId": "298", "endLine": 5, "endColumn": 11}, {"ruleId": "296", "severity": 1, "message": "313", "line": 15, "column": 55, "nodeType": null, "messageId": "298", "endLine": 15, "endColumn": 62}, {"ruleId": "296", "severity": 1, "message": "314", "line": 15, "column": 64, "nodeType": null, "messageId": "298", "endLine": 15, "endColumn": 74}, {"ruleId": "296", "severity": 1, "message": "315", "line": 17, "column": 28, "nodeType": null, "messageId": "298", "endLine": 17, "endColumn": 34}, {"ruleId": "296", "severity": 1, "message": "313", "line": 48, "column": 55, "nodeType": null, "messageId": "298", "endLine": 48, "endColumn": 62}, {"ruleId": "296", "severity": 1, "message": "314", "line": 48, "column": 64, "nodeType": null, "messageId": "298", "endLine": 48, "endColumn": 74}, {"ruleId": "296", "severity": 1, "message": "313", "line": 16, "column": 46, "nodeType": null, "messageId": "298", "endLine": 16, "endColumn": 53}, {"ruleId": "296", "severity": 1, "message": "313", "line": 67, "column": 46, "nodeType": null, "messageId": "298", "endLine": 67, "endColumn": 53}, {"ruleId": "305", "severity": 2, "message": "306", "line": 91, "column": 22, "nodeType": "307", "messageId": "308", "endLine": 91, "endColumn": 25, "suggestions": "316"}, {"ruleId": "296", "severity": 1, "message": "313", "line": 153, "column": 57, "nodeType": null, "messageId": "298", "endLine": 153, "endColumn": 64}, {"ruleId": "296", "severity": 1, "message": "317", "line": 44, "column": 14, "nodeType": null, "messageId": "298", "endLine": 44, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "317", "line": 99, "column": 14, "nodeType": null, "messageId": "298", "endLine": 99, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "318", "line": 19, "column": 10, "nodeType": null, "messageId": "298", "endLine": 19, "endColumn": 12}, {"ruleId": "296", "severity": 1, "message": "319", "line": 25, "column": 3, "nodeType": null, "messageId": "298", "endLine": 25, "endColumn": 15}, {"ruleId": "296", "severity": 1, "message": "320", "line": 201, "column": 43, "nodeType": null, "messageId": "298", "endLine": 201, "endColumn": 51}, {"ruleId": "305", "severity": 2, "message": "306", "line": 221, "column": 33, "nodeType": "307", "messageId": "308", "endLine": 221, "endColumn": 36, "suggestions": "321"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 222, "column": 78, "nodeType": "307", "messageId": "308", "endLine": 222, "endColumn": 81, "suggestions": "322"}, {"ruleId": "296", "severity": 1, "message": "323", "line": 4, "column": 10, "nodeType": null, "messageId": "298", "endLine": 4, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "324", "line": 5, "column": 39, "nodeType": null, "messageId": "298", "endLine": 5, "endColumn": 51}, {"ruleId": "296", "severity": 1, "message": "325", "line": 5, "column": 58, "nodeType": null, "messageId": "298", "endLine": 5, "endColumn": 73}, {"ruleId": "305", "severity": 2, "message": "306", "line": 65, "column": 21, "nodeType": "307", "messageId": "308", "endLine": 65, "endColumn": 24, "suggestions": "326"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 84, "column": 21, "nodeType": "307", "messageId": "308", "endLine": 84, "endColumn": 24, "suggestions": "327"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 190, "column": 49, "nodeType": "330", "messageId": "331", "suggestions": "332"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 20, "column": 10, "nodeType": "307", "messageId": "308", "endLine": 20, "endColumn": 13, "suggestions": "333"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 32, "column": 19, "nodeType": "307", "messageId": "308", "endLine": 32, "endColumn": 22, "suggestions": "334"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 60, "column": 21, "nodeType": "307", "messageId": "308", "endLine": 60, "endColumn": 24, "suggestions": "335"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 66, "column": 24, "nodeType": "307", "messageId": "308", "endLine": 66, "endColumn": 27, "suggestions": "336"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 95, "column": 13, "nodeType": "307", "messageId": "308", "endLine": 95, "endColumn": 16, "suggestions": "337"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 166, "column": 28, "nodeType": "307", "messageId": "308", "endLine": 166, "endColumn": 31, "suggestions": "338"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 207, "column": 24, "nodeType": "307", "messageId": "308", "endLine": 207, "endColumn": 27, "suggestions": "339"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 209, "column": 29, "nodeType": "307", "messageId": "308", "endLine": 209, "endColumn": 32, "suggestions": "340"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 232, "column": 25, "nodeType": "307", "messageId": "308", "endLine": 232, "endColumn": 28, "suggestions": "341"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 234, "column": 12, "nodeType": "307", "messageId": "308", "endLine": 234, "endColumn": 15, "suggestions": "342"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 241, "column": 24, "nodeType": "307", "messageId": "308", "endLine": 241, "endColumn": 27, "suggestions": "343"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 243, "column": 12, "nodeType": "307", "messageId": "308", "endLine": 243, "endColumn": 15, "suggestions": "344"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 250, "column": 27, "nodeType": "307", "messageId": "308", "endLine": 250, "endColumn": 30, "suggestions": "345"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 258, "column": 26, "nodeType": "307", "messageId": "308", "endLine": 258, "endColumn": 29, "suggestions": "346"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 260, "column": 12, "nodeType": "307", "messageId": "308", "endLine": 260, "endColumn": 15, "suggestions": "347"}, {"ruleId": "296", "severity": 1, "message": "303", "line": 4, "column": 63, "nodeType": null, "messageId": "298", "endLine": 4, "endColumn": 73}, {"ruleId": "305", "severity": 2, "message": "306", "line": 7, "column": 34, "nodeType": "307", "messageId": "308", "endLine": 7, "endColumn": 37, "suggestions": "348"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 184, "column": 9, "nodeType": "307", "messageId": "308", "endLine": 184, "endColumn": 12, "suggestions": "349"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 185, "column": 34, "nodeType": "307", "messageId": "308", "endLine": 185, "endColumn": 37, "suggestions": "350"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 188, "column": 24, "nodeType": "307", "messageId": "308", "endLine": 188, "endColumn": 27, "suggestions": "351"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 208, "column": 21, "nodeType": "307", "messageId": "308", "endLine": 208, "endColumn": 24, "suggestions": "352"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 236, "column": 20, "nodeType": "307", "messageId": "308", "endLine": 236, "endColumn": 23, "suggestions": "353"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 241, "column": 20, "nodeType": "307", "messageId": "308", "endLine": 241, "endColumn": 23, "suggestions": "354"}, {"ruleId": "305", "severity": 2, "message": "306", "line": 283, "column": 13, "nodeType": "307", "messageId": "308", "endLine": 283, "endColumn": 16, "suggestions": "355"}, {"ruleId": "296", "severity": 1, "message": "317", "line": 69, "column": 14, "nodeType": null, "messageId": "298", "endLine": 69, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "317", "line": 77, "column": 14, "nodeType": null, "messageId": "298", "endLine": 77, "endColumn": 19}, {"ruleId": "305", "severity": 2, "message": "306", "line": 142, "column": 10, "nodeType": "307", "messageId": "308", "endLine": 142, "endColumn": 13, "suggestions": "356"}, {"ruleId": "296", "severity": 1, "message": "357", "line": 192, "column": 13, "nodeType": null, "messageId": "298", "endLine": 192, "endColumn": 26}, {"ruleId": "296", "severity": 1, "message": "317", "line": 245, "column": 12, "nodeType": null, "messageId": "298", "endLine": 245, "endColumn": 17}, {"ruleId": "296", "severity": 1, "message": "358", "line": 34, "column": 11, "nodeType": null, "messageId": "298", "endLine": 34, "endColumn": 24}, {"ruleId": "296", "severity": 1, "message": "317", "line": 168, "column": 14, "nodeType": null, "messageId": "298", "endLine": 168, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "317", "line": 187, "column": 14, "nodeType": null, "messageId": "298", "endLine": 187, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "317", "line": 206, "column": 14, "nodeType": null, "messageId": "298", "endLine": 206, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'Badge' is defined but never used.", "unusedVar", "'BarChart3' is defined but never used.", "'Database' is defined but never used.", "'Globe' is defined but never used.", "'errors' is assigned a value but never used.", "'ClientType' is defined but never used.", "'validators' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["359", "360"], "'updatedUser' is assigned a value but never used.", "'withAuth' is defined but never used.", "'withRole' is defined but never used.", "'session' is defined but never used.", "'clientInfo' is defined but never used.", "'offset' is assigned a value but never used.", ["361", "362"], "'error' is defined but never used.", "'cn' is defined but never used.", "'ChevronRight' is defined but never used.", "'onToggle' is defined but never used.", ["363", "364"], ["365", "366"], "'useRouter' is defined but never used.", "'LoginRequest' is defined but never used.", "'RegisterRequest' is defined but never used.", ["367", "368"], ["369", "370"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["371", "372", "373", "374"], ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], ["383", "384"], ["385", "386"], ["387", "388"], ["389", "390"], ["391", "392"], ["393", "394"], ["395", "396"], ["397", "398"], ["399", "400"], ["401", "402"], ["403", "404"], ["405", "406"], ["407", "408"], ["409", "410"], ["411", "412"], ["413", "414"], ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], "'password_hash' is assigned a value but never used.", "'clientVersion' is assigned a value but never used.", {"messageId": "423", "fix": "424", "desc": "425"}, {"messageId": "426", "fix": "427", "desc": "428"}, {"messageId": "423", "fix": "429", "desc": "425"}, {"messageId": "426", "fix": "430", "desc": "428"}, {"messageId": "423", "fix": "431", "desc": "425"}, {"messageId": "426", "fix": "432", "desc": "428"}, {"messageId": "423", "fix": "433", "desc": "425"}, {"messageId": "426", "fix": "434", "desc": "428"}, {"messageId": "423", "fix": "435", "desc": "425"}, {"messageId": "426", "fix": "436", "desc": "428"}, {"messageId": "423", "fix": "437", "desc": "425"}, {"messageId": "426", "fix": "438", "desc": "428"}, {"messageId": "439", "data": "440", "fix": "441", "desc": "442"}, {"messageId": "439", "data": "443", "fix": "444", "desc": "445"}, {"messageId": "439", "data": "446", "fix": "447", "desc": "448"}, {"messageId": "439", "data": "449", "fix": "450", "desc": "451"}, {"messageId": "423", "fix": "452", "desc": "425"}, {"messageId": "426", "fix": "453", "desc": "428"}, {"messageId": "423", "fix": "454", "desc": "425"}, {"messageId": "426", "fix": "455", "desc": "428"}, {"messageId": "423", "fix": "456", "desc": "425"}, {"messageId": "426", "fix": "457", "desc": "428"}, {"messageId": "423", "fix": "458", "desc": "425"}, {"messageId": "426", "fix": "459", "desc": "428"}, {"messageId": "423", "fix": "460", "desc": "425"}, {"messageId": "426", "fix": "461", "desc": "428"}, {"messageId": "423", "fix": "462", "desc": "425"}, {"messageId": "426", "fix": "463", "desc": "428"}, {"messageId": "423", "fix": "464", "desc": "425"}, {"messageId": "426", "fix": "465", "desc": "428"}, {"messageId": "423", "fix": "466", "desc": "425"}, {"messageId": "426", "fix": "467", "desc": "428"}, {"messageId": "423", "fix": "468", "desc": "425"}, {"messageId": "426", "fix": "469", "desc": "428"}, {"messageId": "423", "fix": "470", "desc": "425"}, {"messageId": "426", "fix": "471", "desc": "428"}, {"messageId": "423", "fix": "472", "desc": "425"}, {"messageId": "426", "fix": "473", "desc": "428"}, {"messageId": "423", "fix": "474", "desc": "425"}, {"messageId": "426", "fix": "475", "desc": "428"}, {"messageId": "423", "fix": "476", "desc": "425"}, {"messageId": "426", "fix": "477", "desc": "428"}, {"messageId": "423", "fix": "478", "desc": "425"}, {"messageId": "426", "fix": "479", "desc": "428"}, {"messageId": "423", "fix": "480", "desc": "425"}, {"messageId": "426", "fix": "481", "desc": "428"}, {"messageId": "423", "fix": "482", "desc": "425"}, {"messageId": "426", "fix": "483", "desc": "428"}, {"messageId": "423", "fix": "484", "desc": "425"}, {"messageId": "426", "fix": "485", "desc": "428"}, {"messageId": "423", "fix": "486", "desc": "425"}, {"messageId": "426", "fix": "487", "desc": "428"}, {"messageId": "423", "fix": "488", "desc": "425"}, {"messageId": "426", "fix": "489", "desc": "428"}, {"messageId": "423", "fix": "490", "desc": "425"}, {"messageId": "426", "fix": "491", "desc": "428"}, {"messageId": "423", "fix": "492", "desc": "425"}, {"messageId": "426", "fix": "493", "desc": "428"}, {"messageId": "423", "fix": "494", "desc": "425"}, {"messageId": "426", "fix": "495", "desc": "428"}, {"messageId": "423", "fix": "496", "desc": "425"}, {"messageId": "426", "fix": "497", "desc": "428"}, {"messageId": "423", "fix": "498", "desc": "425"}, {"messageId": "426", "fix": "499", "desc": "428"}, "suggestUnknown", {"range": "500", "text": "501"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "502", "text": "503"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "504", "text": "501"}, {"range": "505", "text": "503"}, {"range": "506", "text": "501"}, {"range": "507", "text": "503"}, {"range": "508", "text": "501"}, {"range": "509", "text": "503"}, {"range": "510", "text": "501"}, {"range": "511", "text": "503"}, {"range": "512", "text": "501"}, {"range": "513", "text": "503"}, "replaceWithAlt", {"alt": "514"}, {"range": "515", "text": "516"}, "Replace with `&apos;`.", {"alt": "517"}, {"range": "518", "text": "519"}, "Replace with `&lsquo;`.", {"alt": "520"}, {"range": "521", "text": "522"}, "Replace with `&#39;`.", {"alt": "523"}, {"range": "524", "text": "525"}, "Replace with `&rsquo;`.", {"range": "526", "text": "501"}, {"range": "527", "text": "503"}, {"range": "528", "text": "501"}, {"range": "529", "text": "503"}, {"range": "530", "text": "501"}, {"range": "531", "text": "503"}, {"range": "532", "text": "501"}, {"range": "533", "text": "503"}, {"range": "534", "text": "501"}, {"range": "535", "text": "503"}, {"range": "536", "text": "501"}, {"range": "537", "text": "503"}, {"range": "538", "text": "501"}, {"range": "539", "text": "503"}, {"range": "540", "text": "501"}, {"range": "541", "text": "503"}, {"range": "542", "text": "501"}, {"range": "543", "text": "503"}, {"range": "544", "text": "501"}, {"range": "545", "text": "503"}, {"range": "546", "text": "501"}, {"range": "547", "text": "503"}, {"range": "548", "text": "501"}, {"range": "549", "text": "503"}, {"range": "550", "text": "501"}, {"range": "551", "text": "503"}, {"range": "552", "text": "501"}, {"range": "553", "text": "503"}, {"range": "554", "text": "501"}, {"range": "555", "text": "503"}, {"range": "556", "text": "501"}, {"range": "557", "text": "503"}, {"range": "558", "text": "501"}, {"range": "559", "text": "503"}, {"range": "560", "text": "501"}, {"range": "561", "text": "503"}, {"range": "562", "text": "501"}, {"range": "563", "text": "503"}, {"range": "564", "text": "501"}, {"range": "565", "text": "503"}, {"range": "566", "text": "501"}, {"range": "567", "text": "503"}, {"range": "568", "text": "501"}, {"range": "569", "text": "503"}, {"range": "570", "text": "501"}, {"range": "571", "text": "503"}, {"range": "572", "text": "501"}, {"range": "573", "text": "503"}, [2926, 2929], "unknown", [2926, 2929], "never", [2597, 2600], [2597, 2600], [4305, 4308], [4305, 4308], [4392, 4395], [4392, 4395], [2030, 2033], [2030, 2033], [2620, 2623], [2620, 2623], "&apos;", [5491, 5537], "You don&apos;t have permission to access this page.", "&lsquo;", [5491, 5537], "You don&lsquo;t have permission to access this page.", "&#39;", [5491, 5537], "You don&#39;t have permission to access this page.", "&rsquo;", [5491, 5537], "You don&rsquo;t have permission to access this page.", [376, 379], [376, 379], [613, 616], [613, 616], [1296, 1299], [1296, 1299], [1488, 1491], [1488, 1491], [2146, 2149], [2146, 2149], [3688, 3691], [3688, 3691], [4779, 4782], [4779, 4782], [4835, 4838], [4835, 4838], [5434, 5437], [5434, 5437], [5473, 5476], [5473, 5476], [5668, 5671], [5668, 5671], [5707, 5710], [5707, 5710], [5907, 5910], [5907, 5910], [6128, 6131], [6128, 6131], [6167, 6170], [6167, 6170], [300, 303], [300, 303], [4887, 4890], [4887, 4890], [4925, 4928], [4925, 4928], [5083, 5086], [5083, 5086], [5486, 5489], [5486, 5489], [6261, 6264], [6261, 6264], [6407, 6410], [6407, 6410], [7434, 7437], [7434, 7437], [3660, 3663], [3660, 3663]]