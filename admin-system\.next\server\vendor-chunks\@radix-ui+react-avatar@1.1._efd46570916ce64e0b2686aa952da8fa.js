"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-avatar@1.1._efd46570916ce64e0b2686aa952da8fa";
exports.ids = ["vendor-chunks/@radix-ui+react-avatar@1.1._efd46570916ce64e0b2686aa952da8fa"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._efd46570916ce64e0b2686aa952da8fa/node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-avatar@1.1._efd46570916ce64e0b2686aa952da8fa/node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_ad42a61e498c34b6ab0064ec44eba795/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_cd74c471b1b7daf88f500cd85aa1aa75/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_3aa1064605213fb84b843d985c232dd9/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_6e0f845fa0b5165e723599b67dc13bbf/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-is-hydr_e334ee2d89227f70c6bcc3e423cf1bd1/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)({\n        \"AvatarImage.useCallbackRef[handleLoadingStatusChange]\": (status)=>{\n            onLoadingStatusChange(status);\n            context.onImageLoadingStatusChange(status);\n        }\n    }[\"AvatarImage.useCallbackRef[handleLoadingStatusChange]\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"AvatarImage.useLayoutEffect\": ()=>{\n            if (imageLoadingStatus !== \"idle\") {\n                handleLoadingStatusChange(imageLoadingStatus);\n            }\n        }\n    }[\"AvatarImage.useLayoutEffect\"], [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"AvatarFallback.useEffect\": ()=>{\n            if (delayMs !== void 0) {\n                const timerId = window.setTimeout({\n                    \"AvatarFallback.useEffect.timerId\": ()=>setCanRender(true)\n                }[\"AvatarFallback.useEffect.timerId\"], delayMs);\n                return ({\n                    \"AvatarFallback.useEffect\": ()=>window.clearTimeout(timerId)\n                })[\"AvatarFallback.useEffect\"];\n            }\n        }\n    }[\"AvatarFallback.useEffect\"], [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n    if (!image) {\n        return \"idle\";\n    }\n    if (!src) {\n        return \"error\";\n    }\n    if (image.src !== src) {\n        image.src = src;\n    }\n    return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n    const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n    const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const image = (()=>{\n        if (!isHydrated) return null;\n        if (!imageRef.current) {\n            imageRef.current = new window.Image();\n        }\n        return imageRef.current;\n    })();\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useImageLoadingStatus.useState\": ()=>resolveLoadingStatus(image, src)\n    }[\"useImageLoadingStatus.useState\"]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            setLoadingStatus(resolveLoadingStatus(image, src));\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        src\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)({\n        \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n            const updateStatus = {\n                \"useImageLoadingStatus.useLayoutEffect.updateStatus\": (status)=>({\n                        \"useImageLoadingStatus.useLayoutEffect.updateStatus\": ()=>{\n                            setLoadingStatus(status);\n                        }\n                    })[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"]\n            }[\"useImageLoadingStatus.useLayoutEffect.updateStatus\"];\n            if (!image) return;\n            const handleLoad = updateStatus(\"loaded\");\n            const handleError = updateStatus(\"error\");\n            image.addEventListener(\"load\", handleLoad);\n            image.addEventListener(\"error\", handleError);\n            if (referrerPolicy) {\n                image.referrerPolicy = referrerPolicy;\n            }\n            if (typeof crossOrigin === \"string\") {\n                image.crossOrigin = crossOrigin;\n            }\n            return ({\n                \"useImageLoadingStatus.useLayoutEffect\": ()=>{\n                    image.removeEventListener(\"load\", handleLoad);\n                    image.removeEventListener(\"error\", handleError);\n                }\n            })[\"useImageLoadingStatus.useLayoutEffect\"];\n        }\n    }[\"useImageLoadingStatus.useLayoutEffect\"], [\n        image,\n        crossOrigin,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-avatar@1.1._efd46570916ce64e0b2686aa952da8fa/node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ })

};
;